#!/usr/bin/env python3
"""
Simple build script that creates a macOS app bundle manually
without relying on py2app's complex compilation process.
"""

import os
import sys
import shutil
import subprocess
import stat

def create_app_bundle():
    """Create a simple macOS app bundle."""
    
    # App bundle structure
    app_name = "the tumbled's zoo.app"
    app_path = f"dist/{app_name}"
    contents_path = f"{app_path}/Contents"
    macos_path = f"{contents_path}/MacOS"
    resources_path = f"{contents_path}/Resources"
    
    # Clean and create directories
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    os.makedirs(macos_path)
    os.makedirs(resources_path)
    
    # Copy source files
    shutil.copytree("src", f"{resources_path}/src")
    shutil.copytree("config", f"{resources_path}/config")
    shutil.copy("main.py", f"{resources_path}/main.py")
    
    # Create Info.plist
    info_plist = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleName</key>
    <string>the tumbled's zoo</string>
    <key>CFBundleDisplayName</key>
    <string>the tumbled's zoo</string>
    <key>CFBundleIdentifier</key>
    <string>com.tumbled.zoo</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleExecutable</key>
    <string>the_tumbleds_zoo</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>LSMinimumSystemVersion</key>
    <string>10.12</string>
    <key>CFBundleIconFile</key>
    <string>logo</string>
</dict>
</plist>"""
    
    with open(f"{contents_path}/Info.plist", "w") as f:
        f.write(info_plist)
    
    # Copy icon
    if os.path.exists("config/logo.png"):
        shutil.copy("config/logo.png", f"{resources_path}/logo.png")
    
    # Create launcher script
    launcher_script = f"""#!/bin/bash
# Launcher script for the tumbled's zoo

# Enable error handling
set -e

# Get the directory of this script
DIR="$( cd "$( dirname "${{BASH_SOURCE[0]}}" )" && pwd )"
RESOURCES_DIR="$DIR/../Resources"

# Create log file for debugging
LOG_FILE="$HOME/Library/Logs/tumbleds-zoo.log"
mkdir -p "$HOME/Library/Logs"

# Function to log messages
log_message() {{
    echo "$(date): $1" >> "$LOG_FILE"
}}

log_message "=== Starting the tumbled's zoo ==="
log_message "DIR: $DIR"
log_message "RESOURCES_DIR: $RESOURCES_DIR"

# Check if resources directory exists
if [ ! -d "$RESOURCES_DIR" ]; then
    log_message "ERROR: Resources directory not found: $RESOURCES_DIR"
    exit 1
fi

# Check if main.py exists
if [ ! -f "$RESOURCES_DIR/main.py" ]; then
    log_message "ERROR: main.py not found in: $RESOURCES_DIR"
    exit 1
fi

# Check Python3 availability
if ! command -v python3 &> /dev/null; then
    log_message "ERROR: python3 not found in PATH"
    exit 1
fi

# Check PyQt5 availability
if ! python3 -c "import PyQt5" 2>/dev/null; then
    log_message "ERROR: PyQt5 not available"
    # Try to use user-installed PyQt5
    export PYTHONPATH="$HOME/Library/Python/3.9/lib/python/site-packages:$PYTHONPATH"
    if ! python3 -c "import PyQt5" 2>/dev/null; then
        log_message "ERROR: PyQt5 still not available after adding user site-packages"
        exit 1
    fi
fi

log_message "Python3 found: $(which python3)"
log_message "PyQt5 check passed"

# Set PYTHONPATH to include our source directory
export PYTHONPATH="$RESOURCES_DIR:$PYTHONPATH"
log_message "PYTHONPATH: $PYTHONPATH"

# Change to resources directory
cd "$RESOURCES_DIR"
log_message "Changed to directory: $(pwd)"

# Launch the Python application
log_message "Launching application..."

# Check system architecture and force native execution
ARCH=$(uname -m)
log_message "System architecture: $ARCH"

# Check if we're on Apple Silicon by looking at hardware
HARDWARE=$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "unknown")
log_message "Hardware: $HARDWARE"

# Force ARM64 execution on Apple Silicon Macs regardless of reported architecture
if [[ "$HARDWARE" == *"Apple"* ]] || [[ "$ARCH" == "arm64" ]]; then
    log_message "Detected Apple Silicon, forcing ARM64 execution"
    arch -arm64 python3 main.py 2>&1 | tee -a "$LOG_FILE"
else
    log_message "Running with default architecture"
    python3 main.py 2>&1 | tee -a "$LOG_FILE"
fi
"""
    
    launcher_path = f"{macos_path}/the_tumbleds_zoo"
    with open(launcher_path, "w") as f:
        f.write(launcher_script)
    
    # Make launcher executable
    st = os.stat(launcher_path)
    os.chmod(launcher_path, st.st_mode | stat.S_IEXEC)
    
    print(f"✅ App bundle created successfully at: {app_path}")
    print(f"📁 To install: cp -r '{app_path}' /Applications/")
    
    return True

def main():
    """Main build function."""
    print("🚀 Building the tumbled's zoo app bundle...")
    
    # Check if we're in the right directory
    if not os.path.exists("main.py"):
        print("❌ Error: main.py not found. Please run this script from the unified-ui directory.")
        return False
    
    # Check Python and PyQt5
    try:
        import PyQt5
        print("✅ PyQt5 found")
    except ImportError:
        print("❌ Error: PyQt5 not found. Please install it with: pip install PyQt5")
        return False
    
    # Create the app bundle
    if create_app_bundle():
        print("🎉 Build completed successfully!")
        return True
    else:
        print("❌ Build failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
