#!/bin/bash
# Launcher script for the tumbled's zoo

# Enable error handling
set -e

# Get the directory of this script
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
RESOURCES_DIR="$DIR/../Resources"

# Create log file for debugging
LOG_FILE="$HOME/Library/Logs/tumbleds-zoo.log"
mkdir -p "$HOME/Library/Logs"

# Function to log messages
log_message() {
    echo "$(date): $1" >> "$LOG_FILE"
}

log_message "=== Starting the tumbled's zoo ==="
log_message "DIR: $DIR"
log_message "RESOURCES_DIR: $RESOURCES_DIR"

# Check if resources directory exists
if [ ! -d "$RESOURCES_DIR" ]; then
    log_message "ERROR: Resources directory not found: $RESOURCES_DIR"
    exit 1
fi

# Check if main.py exists
if [ ! -f "$RESOURCES_DIR/main.py" ]; then
    log_message "ERROR: main.py not found in: $RESOURCES_DIR"
    exit 1
fi

# Check Python3 availability
if ! command -v python3 &> /dev/null; then
    log_message "ERROR: python3 not found in PATH"
    exit 1
fi

# Check PyQt5 availability
if ! python3 -c "import PyQt5" 2>/dev/null; then
    log_message "ERROR: PyQt5 not available"
    # Try to use user-installed PyQt5
    export PYTHONPATH="$HOME/Library/Python/3.9/lib/python/site-packages:$PYTHONPATH"
    if ! python3 -c "import PyQt5" 2>/dev/null; then
        log_message "ERROR: PyQt5 still not available after adding user site-packages"
        exit 1
    fi
fi

log_message "Python3 found: $(which python3)"
log_message "PyQt5 check passed"

# Set PYTHONPATH to include our source directory
export PYTHONPATH="$RESOURCES_DIR:$PYTHONPATH"
log_message "PYTHONPATH: $PYTHONPATH"

# Change to resources directory
cd "$RESOURCES_DIR"
log_message "Changed to directory: $(pwd)"

# Launch the Python application
log_message "Launching application..."

# Check system architecture and force native execution
ARCH=$(uname -m)
log_message "System architecture: $ARCH"

# Check if we're on Apple Silicon by looking at hardware
HARDWARE=$(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo "unknown")
log_message "Hardware: $HARDWARE"

# Force ARM64 execution on Apple Silicon Macs regardless of reported architecture
if [[ "$HARDWARE" == *"Apple"* ]] || [[ "$ARCH" == "arm64" ]]; then
    log_message "Detected Apple Silicon, forcing ARM64 execution"
    arch -arm64 python3 main.py 2>&1 | tee -a "$LOG_FILE"
else
    log_message "Running with default architecture"
    python3 main.py 2>&1 | tee -a "$LOG_FILE"
fi
